import asyncio  # Needed for calling async methods...
from bsv import (
    PrivateKey, P2PKH, Transaction, TransactionInput, TransactionOutput
)
from bsv.broadcasters import WhatsOnChainBroadcaster
# Replace with your private key (WIF format)
PRIVATE_KEY = 'L5oBUJejNhGJdS4Zh8wNpmCFyQvK6ARLpuVVQFgo7cQL84Fz6bSC'

# Replace with your source tx which contains UTXO that you want to spend (raw hex format)
SOURCE_TX_HEX = '010000000133f9bb50608f69aa375c0065bf6dbe9af15ba44957df3f13deb8ea2c3a3fa96b000000006b483045022100c0d3a61b2c4b140743867cfd9bd819c34b7e1d2689db3e4ff5eb3b9f38f827720220481f321a47b839fe6dcc81b3972a6173d190b1d4a676ed6ba934e7d017c170d7412102fecc1102a2db47b289fde24b0265a9376bceb4545d59cd95668db12f47bd35e9ffffffff02801a0600000000001976a914761624d83261a91995e092aa1c09c7801c176d8d88ac3c3ba213000000001976a91493fe4e8d230568cd4f351294df741590cb8556d688ac00000000'

async def create_and_broadcast_transaction():
    priv_key = PrivateKey(PRIVATE_KEY)
    source_tx = Transaction.from_hex(SOURCE_TX_HEX)

    tx_input = TransactionInput(
        source_transaction=source_tx,
        source_txid=source_tx.txid(),
        source_output_index=0,
        unlocking_script_template=P2PKH().unlock(priv_key),
    )

    tx_output = TransactionOutput(
        locking_script=P2PKH().lock(priv_key.address()),
        change=True
    )

    tx = Transaction([tx_input], [tx_output], version=1)

    # Set fixed fee of 250 sats
    tx.fee(250)
    tx.sign()

    # Create WhatOnChain broadcaster
    broadcaster = WhatsOnChainBroadcaster()

    # Broadcast transaction
    result = await tx.broadcast(broadcaster)

    # Check if broadcast was successful
    if hasattr(result, 'status') and result.status == 'success':
        print(f"Transaction broadcast successful!")
        print(f"Transaction ID: {result.txid}")
        print(f"Message: {result.message}")
        print(f"Raw hex: {tx.hex()}")
    else:
        print(f"Transaction broadcast failed!")
        print(f"Status: {result.status}")
        print(f"Code: {result.code}")
        print(f"Description: {result.description}")

    return result


if __name__ == "__main__":
    try:
        result = asyncio.run(create_and_broadcast_transaction())
    except Exception as e:
        print(f"Error occurred: {e}")
        print(f"Error type: {type(e).__name__}")
