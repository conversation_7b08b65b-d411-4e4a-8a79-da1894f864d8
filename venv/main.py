import asyncio  # Needed for calling async methods...
from bsv import (
    PrivateKey, P2PKH, Transaction, TransactionInput, TransactionOutput
)
from bsv.broadcasters import WhatsOnChainBroadcaster
from bsv.script import Script
from bsv.utils import encode_pushdata, encode_int
from bsv.constants import OpCode
# Replace with your private key (WIF format)
PRIVATE_KEY = 'L5oBUJejNhGJdS4Zh8wNpmCFyQvK6ARLpuVVQFgo7cQL84Fz6bSC'

# Replace with your source tx which contains UTXO that you want to spend (raw hex format)
SOURCE_TX_HEX = '01000000015cc43b2c4fe2c42b8906acf156e76439a9fe7ca2372083b150317acaf84dd3b0000000006a4730440220439aea1238d608a0b5ccdc3af82d826b418eb95509191e9e35bba6ca7e89d0d902206cec8902768fecb6128a52b88b20b4d0c535c86b003223cd6270972308612db5412103e8d3b6c45f35abe1ccb7f6fb0551eaa072a0bfdf8bff0c083df56eaa59cab234ffffffff0186190600000000001976a914761624d83261a91995e092aa1c09c7801c176d8d88ac00000000'

def create_increment_contract(current_value: int) -> Script:
    """
    Creates a simple increment smart contract that stores a counter value.
    The contract can be unlocked by providing the next incremented value.

    Script: <current_value> OP_1 OP_ADD OP_EQUAL
    This means: "The unlocking script must provide current_value + 1"
    """
    # Build script using bytes directly
    script_bytes = b''

    # Push the current value onto the stack
    script_bytes += encode_int(current_value)

    # Add OP_1 OP_ADD OP_EQUAL
    script_bytes += OpCode.OP_1
    script_bytes += OpCode.OP_ADD
    script_bytes += OpCode.OP_EQUAL

    return Script(script_bytes)

def create_increment_unlock_script(next_value: int) -> Script:
    """
    Creates an unlocking script for the increment contract.
    This provides the next incremented value to unlock the contract.
    """
    return Script(encode_int(next_value))

async def call_increment_contract(contract_txid: str, contract_output_index: int, current_value: int, source_tx_hex: str):
    """
    Calls (spends) the increment smart contract and creates a new one with incremented value.

    :param contract_txid: Transaction ID containing the smart contract output
    :param contract_output_index: Output index of the smart contract (usually 1)
    :param current_value: Current value stored in the contract
    :param source_tx_hex: Raw hex of the transaction containing the contract output
    """
    priv_key = PrivateKey(PRIVATE_KEY)

    print(f"Calling increment contract...")
    print(f"Contract TXID: {contract_txid}")
    print(f"Contract output index: {contract_output_index}")
    print(f"Current value: {current_value}")
    print(f"Next value to provide: {current_value + 1}")

    # Parse the source transaction
    source_tx = Transaction.from_hex(source_tx_hex)

    # Create the unlocking script that provides the next value
    next_value = current_value + 1
    unlock_script = create_increment_unlock_script(next_value)

    print(f"Unlocking script (ASM): {unlock_script.to_asm()}")
    print(f"Unlocking script (hex): {unlock_script.hex()}")

    # Create input that spends the contract output
    contract_input = TransactionInput(
        source_transaction=source_tx,
        source_txid=contract_txid,
        source_output_index=contract_output_index,
        unlocking_script=unlock_script  # Provide the next value directly
    )

    # Create new increment contract output with incremented value
    new_contract_script = create_increment_contract(next_value)
    new_contract_output = TransactionOutput(
        locking_script=new_contract_script,
        satoshis=400  # Smaller amount to leave more room for fees
    )

    # Create change output for any remaining funds
    change_output = TransactionOutput(
        locking_script=P2PKH().lock(priv_key.address()),
        change=True
    )

    # Create the transaction
    tx = Transaction([contract_input], [new_contract_output, change_output], version=1)

    # Set fee and sign
    tx.fee(100)  # Smaller fee to fit in remaining sats
    # Note: We don't call tx.sign() because the contract input has a custom unlocking script

    # Create broadcaster (using default ARC broadcaster for better reliability)
    from bsv.broadcasters import default_broadcaster
    broadcaster = default_broadcaster()

    # Broadcast transaction
    result = await tx.broadcast(broadcaster)

    # Check if broadcast was successful
    if hasattr(result, 'status') and result.status == 'success':
        print(f"\n🎉 Contract call successful!")
        print(f"Transaction ID: {result.txid}")
        print(f"Message: {result.message}")
        print(f"Raw hex: {tx.hex()}")
        print(f"\n--- New Contract Details ---")
        print(f"New contract value: {next_value}")
        print(f"New contract output index: 0")
        print(f"New contract script (ASM): {new_contract_script.to_asm()}")
        print(f"New contract script (hex): {new_contract_script.hex()}")
        print(f"\nTo call again, provide unlocking script with value: {next_value + 1}")
    else:
        print(f"Contract call failed!")
        print(f"Status: {result.status}")
        print(f"Code: {result.code}")
        print(f"Description: {result.description}")

    return result

async def create_and_broadcast_transaction():
    priv_key = PrivateKey(PRIVATE_KEY)
    source_tx = Transaction.from_hex(SOURCE_TX_HEX)

    tx_input = TransactionInput(
        source_transaction=source_tx,
        source_txid=source_tx.txid(),
        source_output_index=0,
        unlocking_script_template=P2PKH().unlock(priv_key),
    )

    # Create change output (P2PKH)
    change_output = TransactionOutput(
        locking_script=P2PKH().lock(priv_key.address()),
        change=True
    )

    # Create increment smart contract output with initial value of 0
    initial_counter_value = 0
    increment_contract_script = create_increment_contract(initial_counter_value)
    contract_output = TransactionOutput(
        locking_script=increment_contract_script,
        satoshis=1000  # 1000 sats for the smart contract
    )

    tx = Transaction([tx_input], [change_output, contract_output], version=1)

    # Set fixed fee of 250 sats
    tx.fee(250)
    tx.sign()

    # Create WhatOnChain broadcaster
    broadcaster = WhatsOnChainBroadcaster()

    # Broadcast transaction
    result = await tx.broadcast(broadcaster)

    # Check if broadcast was successful
    if hasattr(result, 'status') and result.status == 'success':
        print(f"Transaction broadcast successful!")
        print(f"Transaction ID: {result.txid}")
        print(f"Message: {result.message}")
        print(f"Raw hex: {tx.hex()}")
        print(f"\n--- Smart Contract Details ---")
        print(f"Increment contract created with initial value: {initial_counter_value}")
        print(f"Contract output index: 1")
        print(f"Contract amount: 1000 satoshis")
        print(f"Contract script (ASM): {increment_contract_script.to_asm()}")
        print(f"Contract script (hex): {increment_contract_script.hex()}")
        print(f"\nTo spend this contract, provide unlocking script with value: {initial_counter_value + 1}")
    else:
        print(f"Transaction broadcast failed!")
        print(f"Status: {result.status}")
        print(f"Code: {result.code}")
        print(f"Description: {result.description}")

    return result


if __name__ == "__main__":
    try:
        # Uncomment the line below to deploy a new contract
        # result = asyncio.run(create_and_broadcast_transaction())

        # Call the existing increment contract (NEXT CALL: 1 -> 2)
        contract_txid = "517b711ffade868095ddf2cdc4a01fb2fbf66f8f41f3aa89ca8f2ccd3c60b3c2"  # From the successful call
        contract_tx_hex = "01000000015015b808ff0e519230f4678f673cca1b136a52250eeee945c539fb716ccd8b56010000000151ffffffff02f4010000000000000451519387fa000000000000001976a914761624d83261a91995e092aa1c09c7801c176d8d88ac00000000"  # From the successful call
        contract_output_index = 0  # The new smart contract is at output index 0
        current_value = 1  # The contract now has value 1

        print("Making the NEXT increment contract call (1 -> 2):")
        print(f"Contract TXID: {contract_txid}")
        print(f"Current value: {current_value}")
        print(f"Unlocking value: {current_value + 1}")
        print(f"Expected result: Contract with value {current_value + 1}")

        # Call the contract!
        result = asyncio.run(call_increment_contract(contract_txid, contract_output_index, current_value, contract_tx_hex))

    except Exception as e:
        print(f"Error occurred: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()

# can manually update (increment) lines 189 - 193, but fee problem? next value to be 3.
# redeploy at 0 with 10,000 sats?
#   source venv/bin/activate
#   python3 venv/main.py
