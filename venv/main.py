import asyncio  # Needed for calling async methods...
from bsv import (
    PrivateKey, P2PKH, Transaction, TransactionInput, TransactionOutput
)
from bsv.broadcasters import WhatsOnChainBroadcaster
from bsv.script import Script
from bsv.utils import encode_pushdata, encode_int
from bsv.constants import OpCode
# Replace with your private key (WIF format)
PRIVATE_KEY = 'L5oBUJejNhGJdS4Zh8wNpmCFyQvK6ARLpuVVQFgo7cQL84Fz6bSC'

# Replace with your source tx which contains UTXO that you want to spend (raw hex format)
SOURCE_TX_HEX = '01000000015cc43b2c4fe2c42b8906acf156e76439a9fe7ca2372083b150317acaf84dd3b0000000006a4730440220439aea1238d608a0b5ccdc3af82d826b418eb95509191e9e35bba6ca7e89d0d902206cec8902768fecb6128a52b88b20b4d0c535c86b003223cd6270972308612db5412103e8d3b6c45f35abe1ccb7f6fb0551eaa072a0bfdf8bff0c083df56eaa59cab234ffffffff0186190600000000001976a914761624d83261a91995e092aa1c09c7801c176d8d88ac00000000'

def create_increment_contract(current_value: int) -> Script:
    """
    Creates a simple increment smart contract that stores a counter value.
    The contract can be unlocked by providing the next incremented value.

    Script: <current_value> OP_1 OP_ADD OP_EQUAL
    This means: "The unlocking script must provide current_value + 1"
    """
    # Build script using bytes directly
    script_bytes = b''

    # Push the current value onto the stack
    script_bytes += encode_int(current_value)

    # Add OP_1 OP_ADD OP_EQUAL
    script_bytes += OpCode.OP_1
    script_bytes += OpCode.OP_ADD
    script_bytes += OpCode.OP_EQUAL

    return Script(script_bytes)

async def create_and_broadcast_transaction():
    priv_key = PrivateKey(PRIVATE_KEY)
    source_tx = Transaction.from_hex(SOURCE_TX_HEX)

    tx_input = TransactionInput(
        source_transaction=source_tx,
        source_txid=source_tx.txid(),
        source_output_index=0,
        unlocking_script_template=P2PKH().unlock(priv_key),
    )

    # Create change output (P2PKH)
    change_output = TransactionOutput(
        locking_script=P2PKH().lock(priv_key.address()),
        change=True
    )

    # Create increment smart contract output with initial value of 0
    initial_counter_value = 0
    increment_contract_script = create_increment_contract(initial_counter_value)
    contract_output = TransactionOutput(
        locking_script=increment_contract_script,
        satoshis=1000  # 1000 sats for the smart contract
    )

    tx = Transaction([tx_input], [change_output, contract_output], version=1)

    # Set fixed fee of 250 sats
    tx.fee(250)
    tx.sign()

    # Create WhatOnChain broadcaster
    broadcaster = WhatsOnChainBroadcaster()

    # Broadcast transaction
    result = await tx.broadcast(broadcaster)

    # Check if broadcast was successful
    if hasattr(result, 'status') and result.status == 'success':
        print(f"Transaction broadcast successful!")
        print(f"Transaction ID: {result.txid}")
        print(f"Message: {result.message}")
        print(f"Raw hex: {tx.hex()}")
        print(f"\n--- Smart Contract Details ---")
        print(f"Increment contract created with initial value: {initial_counter_value}")
        print(f"Contract output index: 1")
        print(f"Contract amount: 1000 satoshis")
        print(f"Contract script (ASM): {increment_contract_script.to_asm()}")
        print(f"Contract script (hex): {increment_contract_script.hex()}")
        print(f"\nTo spend this contract, provide unlocking script with value: {initial_counter_value + 1}")
    else:
        print(f"Transaction broadcast failed!")
        print(f"Status: {result.status}")
        print(f"Code: {result.code}")
        print(f"Description: {result.description}")

    return result


if __name__ == "__main__":
    try:
        result = asyncio.run(create_and_broadcast_transaction())
    except Exception as e:
        print(f"Error occurred: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
